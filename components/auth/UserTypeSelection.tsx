"use client"

import { useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import type { UserType } from "@/lib/types"
import { Truck, Users, User, Shield } from "lucide-react"

const userTypes = [
  {
    type: "buyer" as UserType,
    title: "Demand Creator",
    description: "Companies like Swiggy, Zomato, Blinkit that need delivery riders",
    icon: Truck,
    color: "from-blue-500 to-blue-600",
    bgColor: "bg-blue-50",
    textColor: "text-blue-600",
    examples: ["<PERSON>wiggy", "Zomato", "Blinkit", "Amazon"],
  },
  {
    type: "supplier" as UserType,
    title: "Fleet Manager",
    description: "Middlemen managing multiple riders and fleet operations",
    icon: Users,
    color: "from-green-500 to-green-600",
    bgColor: "bg-green-50",
    textColor: "text-green-600",
    examples: ["Yan<PERSON>", "Rapid Riders", "Fleet Services"],
  },
  {
    type: "rider" as UserType,
    title: "Solo Rider",
    description: "Individual delivery agents looking for flexible gig opportunities",
    icon: User,
    color: "from-orange-500 to-orange-600",
    bgColor: "bg-orange-50",
    textColor: "text-orange-600",
    examples: ["Individual Riders", "Freelance Delivery"],
  },
  {
    type: "admin" as UserType,
    title: "Platform Admin",
    description: "Administrative access to manage the entire FleetConnect platform",
    icon: Shield,
    color: "from-purple-500 to-purple-600",
    bgColor: "bg-purple-50",
    textColor: "text-purple-600",
    examples: ["Platform Management", "System Administration"],
  },
]

export default function UserTypeSelection() {
  const router = useRouter()

  const handleUserTypeClick = (userType: UserType) => {
    router.push(`/${userType}/login`)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <div className="w-full max-w-7xl">
        {/* Header */}
        <div className="text-center mb-16">
          <div className="flex items-center justify-center mb-6">
            <div className="w-16 h-16 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-2xl flex items-center justify-center shadow-lg">
              <Truck className="w-8 h-8 text-white" />
            </div>
          </div>
          <h1 className="text-5xl md:text-6xl font-bold text-gray-900 mb-6">
            Welcome to{" "}
            <span className="bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
              FleetConnect
            </span>
          </h1>
          <p className="text-xl md:text-2xl text-gray-600 max-w-3xl mx-auto mb-8">
            The ultimate platform connecting delivery demand with fleet supply across India
          </p>
          <div className="inline-flex items-center bg-yellow-100 text-yellow-800 px-6 py-3 rounded-full text-sm font-medium">
            <span className="mr-2">🚀</span>
            Demo Version - Choose your user type to continue
          </div>
        </div>

        {/* User Type Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
          {userTypes.map((userType) => {
            const Icon = userType.icon

            return (
              <Card
                key={userType.type}
                className="cursor-pointer transition-all duration-300 hover:shadow-2xl hover:-translate-y-2 border-0 bg-white/80 backdrop-blur-sm"
                onClick={() => handleUserTypeClick(userType.type)}
              >
                <CardHeader className="text-center pb-6">
                  <div
                    className={`w-20 h-20 bg-gradient-to-r ${userType.color} rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg`}
                  >
                    <Icon className="w-10 h-10 text-white" />
                  </div>
                  <CardTitle className="text-2xl font-bold text-gray-900 mb-3">{userType.title}</CardTitle>
                  <CardDescription className="text-gray-600 text-base leading-relaxed">
                    {userType.description}
                  </CardDescription>
                </CardHeader>
                <CardContent className="pt-0">
                  <div className={`${userType.bgColor} rounded-xl p-4`}>
                    <p className={`font-semibold ${userType.textColor} mb-2`}>Examples:</p>
                    <p className="text-gray-700 text-sm">{userType.examples.join(", ")}</p>
                  </div>
                </CardContent>
              </Card>
            )
          })}
        </div>



        {/* Footer */}
        <div className="text-center text-gray-500">
          <p className="mb-2">
            This is a demo application showcasing the Fleet Management Aggregator Platform concept.
          </p>
          <p>All data is mocked and no real transactions are processed.</p>
        </div>
      </div>
    </div>
  )
}
