'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import DashboardLayout from '@/components/layout/DashboardLayout'
import { authService } from '@/lib/auth'

export default function RiderLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const router = useRouter()
  const [currentUser, setCurrentUser] = useState(authService.getCurrentUser())
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const user = authService.getCurrentUser()
    setCurrentUser(user)
    
    // For demo purposes, if no user is logged in, redirect to login
    if (!user) {
      router.push("/rider/login")
      return
    }
    
    // If user is logged in but wrong type, redirect to their dashboard
    if (user.type !== "rider") {
      router.push(`/${user.type}/dashboard`)
      return
    }

    setIsLoading(false)
  }, [router])

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-muted-foreground">Loading...</p>
        </div>
      </div>
    )
  }

  if (!currentUser || currentUser.type !== "rider") {
    return null // Will redirect in useEffect
  }

  return (
    <DashboardLayout userType="rider">
      {children}
    </DashboardLayout>
  )
}
